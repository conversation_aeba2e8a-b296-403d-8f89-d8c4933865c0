# 物业管理系统项目文档

## 项目概述

物业管理系统是一个基于Java Swing开发的桌面应用程序，旨在帮助物业公司高效管理小区业主信息、报修服务、缴费管理等日常业务。系统采用MVC架构设计，使用MySQL数据库存储数据，提供了友好的用户界面和完善的功能模块。

## 项目结构

项目采用标准的Java包结构组织代码，主要包括以下几个部分：

```
src/main/java/com/property/
├── dao/                # 数据访问对象层，处理数据库操作
│   ├── FeeTypeDAO.java         # 费用类型数据访问对象
│   ├── OwnerDao.java           # 业主信息数据访问对象
│   ├── PaymentBillDAO.java     # 缴费账单数据访问对象
│   └── PaymentRecordDAO.java   # 缴费记录数据访问对象
├── model/              # 模型层，定义数据实体类
│   ├── Account.java            # 账户实体类
│   ├── FeeType.java            # 费用类型实体类
│   ├── Owner.java              # 业主信息实体类
│   ├── PaymentBill.java        # 缴费账单实体类
│   └── PaymentRecord.java      # 缴费记录实体类
├── service/            # 服务层，处理业务逻辑
│   └── PaymentService.java     # 缴费相关业务逻辑服务
├── util/               # 工具类
│   ├── BackgroundPanel.java    # 背景面板工具类
│   ├── DatabaseConfig.java     # 数据库配置类
│   ├── DatabaseConnection.java # 数据库连接工具类
│   ├── DialogUtil.java         # 对话框工具类
│   ├── GUIUtil.java            # GUI工具类
│   ├── PasswordService.java    # 密码服务工具类
│   └── UIStyleUtil.java        # UI样式工具类
├── view/               # 视图层，用户界面
│   ├── AddOwnerView.java           # 添加业主界面
│   ├── ChangePasswordView.java     # 修改密码界面
│   ├── FeeTypeManagementView.java  # 费用类型管理界面
│   ├── LoginView.java              # 登录界面
│   ├── MainView.java               # 主界面
│   ├── OwnerInfoView.java          # 业主信息界面
│   ├── PaymentBillManagementView.java  # 账单管理界面
│   ├── PaymentManagementView.java      # 缴费管理界面
│   ├── PaymentRecordManagementView.java # 缴费记录管理界面
│   ├── RegisterView.java           # 注册界面
│   ├── RepairRequestView.java      # 报修请求界面
│   ├── RepairServiceView.java      # 报修服务界面
│   └── UserPaymentQueryView.java   # 用户缴费查询界面
└── app.java            # 应用程序入口
```

## 系统功能

系统提供了以下主要功能：

1. **用户管理**：
   - 用户注册：普通用户可以注册账号
   - 用户登录：支持管理员和普通用户登录
   - 密码修改：用户可以修改自己的密码

2. **业主信息管理**：
   - 添加业主：录入业主基本信息
   - 查询业主：根据姓名查询业主信息
   - 删除业主：删除业主及相关信息

3. **报修服务**：
   - 提交报修：普通用户可以提交报修请求
   - 查看报修：管理员可以查看所有报修请求
   - 处理报修：管理员可以处理报修请求

4. **缴费管理**：
   - 费用类型管理：添加、编辑、删除费用类型
   - 账单管理：生成账单、查看账单详情、处理缴费
   - 缴费记录管理：查看缴费记录、打印收据，界面风格与其他缴费管理界面保持一致
   - 用户缴费查询：普通用户查询自己的缴费信息，支持在线缴费

## 系统架构

系统采用经典的三层架构设计：

1. **表示层（View）**：
   - 基于Java Swing构建用户界面
   - 采用统一的界面风格设计
   - 根据用户角色显示不同的功能菜单

2. **业务逻辑层（Service）**：
   - 处理核心业务逻辑
   - 连接表示层和数据访问层
   - 实现数据验证和业务规则

3. **数据访问层（DAO）**：
   - 封装数据库操作
   - 提供CRUD（创建、读取、更新、删除）功能
   - 处理数据库事务和异常

## 数据库设计

系统使用MySQL数据库，主要包含以下表：

### 管理员账户表 (t_admin_account)
- id：自增主键
- user_name：用户名（唯一索引）
- password：密码
- create_time：创建时间
- update_time：更新时间

### 业主信息表 (t_owner_info)
- id：自增主键
- user_name：用户名
- password：密码
- name：业主姓名
- gender：性别
- house_number：门牌号
- phone：联系电话
- id_number：身份证号
- remark：备注信息
- emergency_contact：紧急联系人
- emergency_phone：紧急联系人电话
- create_time：创建时间
- update_time：更新时间

### 报修请求表 (t_repair_request)
- id：自增主键
- name：业主姓名
- house_number：门牌号
- repair_content：报修内容
- status：状态（待处理、已处理）
- create_time：创建时间
- update_time：更新时间

### 费用类型表 (t_fee_type)
- id：自增主键
- name：费用类型名称
- description：描述
- unit_price：单价
- billing_cycle：计费周期
- billing_method：计费方式
- status：状态（启用、禁用）
- create_time：创建时间
- update_time：更新时间

### 账单表 (t_payment_bill)
- id：自增主键
- bill_no：账单编号（唯一索引）
- owner_id：业主ID（外键，级联删除）
- fee_type_id：费用类型ID（外键）
- billing_period：账单周期
- amount：应缴金额
- paid_amount：已缴金额
- status：状态（未缴费、部分缴费、已缴费）
- due_date：缴费截止日期
- remark：备注
- create_time：创建时间
- update_time：更新时间

### 缴费记录表 (t_payment_record)
- id：自增主键
- record_no：记录编号（唯一索引）
- bill_id：账单ID（外键，级联删除）
- bill_no：账单编号
- owner_id：业主ID
- owner_name：业主姓名
- payment_amount：缴费金额
- payment_method：缴费方式
- payment_time：缴费时间
- operator：操作员
- receipt_no：收据编号
- remark：备注
- create_time：创建时间
- update_time：更新时间

## E-R图

### 实体分析

物业管理系统包含以下主要实体：

#### 1. 业主信息 (Owner)
- **主键**：id (业主ID)
- **属性**：
  - user_name (用户名)
  - password (密码)
  - name (姓名)
  - gender (性别)
  - house_number (门牌号)
  - phone (联系电话)
  - id_number (身份证号)
  - remark (备注)
  - emergency_contact (紧急联系人)
  - emergency_phone (紧急联系电话)
  - create_time (创建时间)
  - update_time (更新时间)

#### 3. 管理员账户 (AdminAccount)
- **主键**：id (管理员ID)
- **属性**：
  - user_name (管理员用户名)
  - password (管理员密码)
  - create_time (创建时间)
  - update_time (更新时间)

#### 4. 报修请求 (RepairRequest)
- **主键**：id (报修ID)
- **属性**：
  - name (业主姓名)
  - house_number (门牌号)
  - repair_content (报修内容)
  - status (处理状态：待处理、处理中、已完成)
  - owner_id (关联业主ID，外键)
  - create_time (创建时间)
  - update_time (更新时间)

#### 5. 费用类型 (FeeType)
- **主键**：id (费用类型ID)
- **属性**：
  - name (费用类型名称)
  - description (费用描述)
  - unit_price (单价)
  - billing_cycle (计费周期)
  - calculation_method (计费方式)
  - is_active (是否启用)
  - create_time (创建时间)
  - update_time (更新时间)

#### 6. 缴费账单 (PaymentBill)
- **主键**：id (账单ID)
- **属性**：
  - bill_no (账单编号)
  - owner_id (业主ID，外键)
  - fee_type_id (费用类型ID，外键)
  - billing_period (账单周期)
  - amount (应缴金额)
  - paid_amount (已缴金额)
  - status (状态：未缴费、部分缴费、已缴费)
  - due_date (缴费截止日期)
  - remark (备注)
  - create_time (创建时间)
  - update_time (更新时间)

#### 7. 缴费记录 (PaymentRecord)
- **主键**：id (记录ID)
- **属性**：
  - record_no (缴费记录编号)
  - bill_id (关联账单ID，外键)
  - owner_id (业主ID，外键)
  - payment_amount (缴费金额)
  - payment_method (缴费方式)
  - payment_time (缴费时间)
  - operator (操作员)
  - receipt_no (收据编号)
  - remark (备注)
  - create_time (创建时间)
  - update_time (更新时间)

### 实体关系分析（基于数据流分析）

#### 1. 业主信息 与 用户账户 (1:1)
- **关系类型**：一对一关系
- **外键约束**：用户账户表的owner_id引用业主信息表的id
- **级联操作**：删除业主时级联删除对应的用户账户
- **业务含义**：每个业主拥有一个登录账户
- **数据流**：用户注册 → 创建业主信息 → 创建用户账户；用户登录 → 验证用户账户 → 关联业主信息

#### 2. 业主信息 与 报修请求 (1:N)
- **关系类型**：一对多关系
- **外键约束**：报修请求表的owner_id引用业主信息表的id
- **级联操作**：删除业主时级联删除所有相关报修请求
- **业务含义**：一个业主可以提交多个报修请求
- **数据流**：业主登录 → 提交报修 → 创建报修请求记录 → 关联业主ID

#### 3. 业主信息 与 缴费账单 (1:N)
- **关系类型**：一对多关系
- **外键约束**：缴费账单表的owner_id引用业主信息表的id
- **级联操作**：删除业主时级联删除所有相关账单
- **业务含义**：一个业主可以有多个缴费账单
- **数据流**：管理员生成账单 → 选择费用类型和业主 → 创建账单记录 → 关联业主ID和费用类型ID

#### 4. 费用类型 与 缴费账单 (1:N)
- **关系类型**：一对多关系
- **外键约束**：缴费账单表的fee_type_id引用费用类型表的id
- **级联操作**：删除费用类型时限制删除（RESTRICT）
- **业务含义**：一种费用类型可以生成多个账单
- **数据流**：管理员创建费用类型 → 批量生成账单 → 根据费用类型计算金额

#### 5. 缴费账单 与 缴费记录 (1:N)
- **关系类型**：一对多关系
- **外键约束**：缴费记录表的bill_id引用缴费账单表的id
- **级联操作**：删除账单时级联删除相关缴费记录
- **业务含义**：一个账单可以有多次缴费记录（支持分期缴费）
- **数据流**：业主缴费 → 选择账单 → 创建缴费记录 → 更新账单状态和已缴金额

#### 6. 业主信息 与 缴费记录 (1:N)
- **关系类型**：一对多关系
- **外键约束**：缴费记录表的owner_id引用业主信息表的id
- **级联操作**：删除业主时级联删除所有相关缴费记录
- **业务含义**：一个业主可以有多个缴费记录
- **数据流**：业主缴费 → 验证业主身份 → 创建缴费记录 → 关联业主ID和账单ID

### E-R图表示

#### Mermaid ER图

```mermaid
erDiagram
    ADMIN_ACCOUNT {
        int id PK "管理员ID"
        varchar user_name UK "用户名"
        varchar password "密码"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
    }

    OWNER_INFO {
        int id PK "业主ID"
        varchar name UK "业主姓名"
        varchar gender "性别"
        varchar house_number "门牌号"
        varchar phone "联系电话"
        varchar id_number "身份证号"
        text remark "备注信息"
        varchar emergency_contact "紧急联系人"
        varchar emergency_phone "紧急联系人电话"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
    }

    USER_ACCOUNT {
        int id PK "用户账户ID"
        varchar user_name UK "用户名"
        varchar password "密码"
        int owner_id FK "业主ID"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
    }

    REPAIR_REQUEST {
        int id PK "报修ID"
        varchar name "业主姓名"
        varchar house_number "门牌号"
        text repair_content "报修内容"
        varchar status "状态"
        int owner_id FK "业主ID"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
    }

    FEE_TYPE {
        int id PK "费用类型ID"
        varchar name UK "费用类型名称"
        text description "描述"
        decimal unit_price "单价"
        varchar billing_cycle "计费周期"
        varchar calculation_method "计费方式"
        tinyint is_active "是否启用"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
    }

    PAYMENT_BILL {
        int id PK "账单ID"
        varchar bill_no UK "账单编号"
        int owner_id FK "业主ID"
        int fee_type_id FK "费用类型ID"
        varchar billing_period "账单周期"
        decimal amount "应缴金额"
        decimal paid_amount "已缴金额"
        varchar status "状态"
        date due_date "缴费截止日期"
        text remark "备注"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
    }

    PAYMENT_RECORD {
        int id PK "记录ID"
        varchar record_no UK "记录编号"
        int bill_id FK "账单ID"
        int owner_id FK "业主ID"
        decimal payment_amount "缴费金额"
        varchar payment_method "缴费方式"
        datetime payment_time "缴费时间"
        varchar operator "操作员"
        varchar receipt_no "收据编号"
        text remark "备注"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
    }

    %% 关系定义 - 基于实际数据流分析
    OWNER_INFO ||--|| USER_ACCOUNT : "拥有登录账户"
    OWNER_INFO ||--o{ REPAIR_REQUEST : "提交报修"
    OWNER_INFO ||--o{ PAYMENT_BILL : "产生账单"
    OWNER_INFO ||--o{ PAYMENT_RECORD : "缴费记录"
    FEE_TYPE ||--o{ PAYMENT_BILL : "生成账单"
    PAYMENT_BILL ||--o{ PAYMENT_RECORD : "缴费明细"
```

#### 实体关系图说明

**图形化E-R图说明**：
- 矩形框表示实体
- 线条表示实体间的关系
- PK表示主键，FK表示外键，UK表示唯一键
- 关系线上的符号表示关系类型（1:1, 1:N等）
- ||--o{ 表示一对多关系

### 关系约束说明

1. **级联删除约束**：
   - 删除业主信息时，自动删除相关的用户账户、报修请求、缴费账单和缴费记录
   - 确保数据一致性，避免孤立数据

2. **限制删除约束**：
   - 费用类型被账单引用时不能删除
   - 账单被缴费记录引用时不能删除
   - 保护重要的基础数据

3. **唯一性约束**：
   - 业主姓名唯一
   - 用户名唯一
   - 管理员用户名唯一
   - 账单编号唯一
   - 缴费记录编号唯一

4. **数据完整性**：
   - 所有外键关系都有相应的约束
   - 重要字段设置为NOT NULL
   - 金额字段使用DECIMAL类型确保精度

## 界面功能概述

系统提供了以下主要界面：

1. **登录界面**：支持管理员和普通用户登录，提供用户名和密码验证
2. **注册界面**：支持新用户注册，包含用户名重复检查
3. **主界面**：
   - 根据用户类型（管理员/普通用户）显示不同的菜单项
   - 管理员可见：账户设置、业主信息、报修请求、缴费管理
   - 普通用户可见：账户设置、保修服务、缴费查询
4. **业主信息管理**：支持添加、查询和删除业主信息，列表中显示业主ID
5. **保修服务**：提供报修信息提交功能
6. **报修请求**：管理报修请求，支持查询和删除功能
7. **修改密码**：提供密码修改功能
8. **缴费管理**：
   - **费用类型管理**：支持添加、编辑、删除费用类型
   - **账单管理**：支持生成账单、查看账单详情、缴费处理
   - **缴费记录管理**：查看缴费记录、打印收据，界面风格与其他缴费管理界面保持一致
9. **用户缴费查询**：
   - 普通用户专用界面，只能查看自己的缴费信息
   - 支持查看账单详情和缴费记录
   - 支持在线缴费功能，可以直接进行缴费操作

## 界面风格设计

系统采用统一的界面风格设计，主要特点包括：

1. **配色方案**：
   - 主色调：深蓝色（#2980b9）
   - 次要色调：浅蓝色（#3498db）
   - 强调色：橙色（#f39c12）
   - 警告色：红色（#e74c3c）

2. **字体样式**：
   - 标题：微软雅黑，粗体，24px
   - 菜单：微软雅黑，粗体，16px
   - 正文：微软雅黑，常规，14px
   - 标签：微软雅黑，常规，14px

3. **组件样式**：
   - 按钮：圆角设计，悬停效果
   - 表格：条纹效果，突出显示选中行
   - 面板：阴影效果，圆角边框
   - 对话框：渐变背景，阴影效果

4. **布局特点**：
   - 标题栏：顶部深蓝色标题栏
   - 内容区：白色背景，适当留白
   - 按钮区：右对齐，突出主要操作按钮
   - 表单：标签右对齐，输入框左对齐

## 系统使用说明

### 系统要求

- Java Runtime Environment (JRE) 17或更高版本
- MySQL 8.3或更高版本
- 最小屏幕分辨率：1024x768

### 安装步骤

1. **数据库配置**：
   - 创建名为`mysql_assignment`的MySQL数据库
   - 导入项目根目录下的`database.sql`文件，创建所需表结构
   - 根据需要修改`src/main/java/com/property/util/DatabaseConfig.java`中的数据库连接信息
2. **编译和运行**：
   - 使用Java IDE（如Eclipse、IntelliJ IDEA）打开项目
   - 编译项目，生成可执行文件
   - 运行`app.java`启动系统

### 用户指南

1. **登录系统**：
   - 管理员账户：用户名`admin`，密码`admin`
   - 普通用户：可以通过注册功能创建新账户

2. **管理员功能**：
   - **业主信息管理**：添加、查询和删除业主信息
   - **报修请求管理**：查看和处理业主提交的报修请求
   - **缴费管理**：
     - 费用类型管理：设置不同类型的费用
     - 账单管理：生成账单、处理缴费
     - 缴费记录管理：查看缴费记录、打印收据

3. **普通用户功能**：
   - **账户设置**：修改密码
   - **报修服务**：提交报修请求
   - **缴费查询**：查看自己的账单、进行在线缴费

### 常见问题解答

1. **忘记密码怎么办？**
   - 普通用户：请联系管理员重置密码
   - 管理员：请检查数据库中的`t_admin_account`表，直接修改密码字段

2. **系统无法连接到数据库？**
   - 检查MySQL服务是否正常运行
   - 验证数据库连接信息是否正确
   - 确认数据库用户是否有足够的权限

3. **如何备份数据？**
   - 使用MySQL的备份工具导出数据库：
     ```
     mysqldump -u username -p mysql_assignment > backup.sql
     ```

## 开发技术

- **编程语言**：Java 8
- **GUI框架**：Java Swing
- **数据库**：MySQL 8.3
- **构建工具**：Maven
- **开发环境**：Eclipse/IntelliJ IDEA
- **版本控制**：Git

## 项目团队

- 杨爽
- 杨璜萍
- 李思涵

## 版权信息

© 2025 物业管理系统 - 数据库课程期末实训